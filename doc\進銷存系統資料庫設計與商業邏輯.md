# 進銷存系統資料庫設計與商業邏輯

# 1. 商業邏輯

### 1.1 系統目標

本進銷存系統旨在管理庫存品在多倉庫中的流動，處理與合作夥伴（供應商和客戶）的交易，並支援加工、包裝轉換、財務管理、盤點、毛利計算及錯誤處理。系統需具備靈活性、可追溯性和資料一致性，以滿足以下業務需求：

- 管理合作夥伴的多重身份（客戶、供應商）。
- 支援庫存品分類、價格管理和多倉庫庫存。
- 提供採購、銷售、退貨、加工、包裝轉換和倉庫調撥功能。
- 計算銷售毛利並支援關帳與應收應付帳款。
- 處理用戶輸入錯誤，確保庫存和財務準確。

### 1.2 核心業務流程

### 1.2.1 合作夥伴管理

- **合作夥伴（Partner）**：儲存所有合作夥伴的基本資訊（如名稱、地址），支援組織（含台灣統一編號、稅籍編號）和個人（含身分證字號）。
- **客戶（CustomerDetail）**：記錄進銷存相關屬性（如忠誠積分）。
- **供應商（SupplierDetail）**：記錄合約條款、評級等。
- **聯絡人（ContactPerson）**：透過多對多關聯（`SupplierContact` 和 `CustomerContact`）管理外部聯絡人，支援第三方代理或多部門場景。

### 1.2.2 庫存品與價格管理

- **庫存品（Item）**：儲存庫存品資訊，關聯單一分類（`ItemCategory`），包含標準成本（`StandardCost`）以支援成本計算。
- **分類（ItemCategory）**：支援階層結構，透過 `ParentCategoryId` 實現父子關係。
- **價格（ItemPrice）**：記錄多種價格類型（`PriceType`），支援時間性價格（`EffectiveDate` 和 `ExpirationDate`）。

### 1.2.3 倉庫與庫存管理

- **倉庫（Warehouse）**：定義倉庫，支援不同成本計算方法（FIFO、LIFO、加權平均、標準成本）。
- **庫存（Inventory）**：記錄每個倉庫的庫存品總量和加權平均成本（若適用）。
- **批次（ItemBatch，原 ItemLot）**：追蹤進貨批次，記錄數量、成本和入庫日期，支援成本計算和追蹤。
- **調撥（ItemTransfer）**：管理倉庫間庫存品移動，影響庫存和批次。
- **盤點（InventoryAdjustment）**：定期或臨時檢查庫存，調整差異，影響庫存和財務。

### 1.2.4 採購與銷售流程

- **採購**：`PurchaseOrder` → `ItemReceipt` → `PurchaseReturn`（可選關聯訂單）。
- **銷售**：`SalesOrder` → `Shipment` → `SalesReturn`（可選關聯訂單），記錄毛利（`SalesOrderDetail.GrossProfit`）。

### 1.2.5 加工與包裝轉換

- **物料清單（Bom）**：定義成品與原料關係。
- **加工訂單（ProcessingOrder）**：管理加工或包裝轉換，記錄原料（`ProcessingOrderInput`）和成品（`ProcessingOrderOutput`）。

### 1.2.6 財務管理

- **應收應付帳款（AccountsReceivable, AccountsPayable）**：追蹤客戶與供應商帳款。
- **付款（Payment）**：記錄實際交易。
- **關帳（AccountClosing）**：總結成本、收入和利潤。

### 1.2.7 錯誤處理

- **輸入驗證**：前端和後端檢查庫存品、數量、價格等。
- **訂單修改**：訂單確認後但未入庫/出貨前允許修改，記錄至 `OrderLog`。
- **入庫/出貨後**：透過退貨（`PurchaseReturn`、`SalesReturn`）或盤點（`InventoryAdjustment`）修正。
- **財務調整**：同步更新帳款和毛利。

### 1.2.8 毛利計算

- 毛利 = 銷售收入（`SalesOrderDetail.UnitPrice × Quantity`） - 銷售成本（根據 `Warehouse.CostMethod` 計算）。
- 成本來源：
    - **FIFO/LIFO**：從 `ItemBatch.UnitCost`。
    - **加權平均**：從 `Inventory.AverageCost`。
    - **標準成本**：從 `Item.StandardCost`。
- 記錄於 `SalesOrderDetail.UnitCost` 和 `GrossProfit`。

### 1.2.9 盤點

- 盤點單（`InventoryAdjustment`）記錄倉庫、日期和原因。
- 盤點明細（`InventoryAdjustmentDetail`）記錄實際數量、系統數量和差異，調整 `Inventory` 和 `ItemBatch`。

### 1.2.10 ItemBatch 運行模式

`ItemBatch` 追蹤庫存品批次，支援以下成本計算方法：

- **FIFO（先入先出）**：
    - 入庫：生成 `ItemBatch`，記錄實際成本（`UnitCost`）。
    - 出貨：優先扣除最早的 `ItemBatch`（按 `ReceiptDate` 升序）。
    - 調撥：移動指定 `ItemBatch`。
    - 盤點：調整最早/指定批次。
    - 成本：使用 `ItemBatch.UnitCost`。
- **LIFO（後入先出）**：
    - 同 FIFO，但優先扣除最晚批次（按 `ReceiptDate` 降序）。
- **加權平均**：
    - 入庫：更新 `Inventory.AverageCost` = (現有總成本 + 新批次總成本) / (現有數量 + 新批次數量)。
    - 出貨：使用 `Inventory.AverageCost`，不直接扣除 `ItemBatch`。
    - 調撥/盤點：使用平均成本。
- **標準成本**：
    - 入庫：記錄實際成本到 `ItemBatch`，但庫存成本使用 `Item.StandardCost`。
    - 出貨：成本從 `Item.StandardCost`，僅更新 `Inventory.Quantity`。
    - 調撥/盤點：使用 `Item.StandardCost`。
    - 成本差異：記錄於 `AccountClosing` 或單獨表（若需要）。

---

## 2. 命名規範

- **表名**：單數形式，PascalCase（例如 `Partner`、`ItemReceipt`）。
- **主鍵**：`<表名>Id`（例如 `PartnerId`、`ItemId`），型態為 `VARCHAR(36)`（對應 C# `Guid`）。
- **外鍵**：`<相關表名>Id`（例如 `WarehouseId`）。
- **欄位**：PascalCase，反映業務含義（例如 `Quantity`、`UnitPrice`）。
- **資料型態**：
    - 主鍵/外鍵：`VARCHAR(36)`。
    - 數值：`DECIMAL(18,2)`（價格、數量）或 `INT`（整數）。
    - 日期：`DATETIME`。
    - 文字：`VARCHAR` 或 `TEXT`。

---

## 3. 資料表結構

以下是完整的 SQL 資料表結構，整合所有功能。

```sql
-- 合作夥伴相關表
CREATE TABLE Partner (
    PartnerId VARCHAR(36) PRIMARY KEY,
    Name VARCHAR(255) NOT NULL,
    Address TEXT,
    ContactInfo VARCHAR(255)
);

CREATE TABLE OrganizationDetail (
    PartnerId VARCHAR(36) PRIMARY KEY,
    BusinessId VARCHAR(8),  -- 統一編號
    TaxId VARCHAR(10),      -- 稅籍編號
    LegalRepresentative VARCHAR(255),
    FOREIGN KEY (PartnerId) REFERENCES Partner(PartnerId)
);

CREATE TABLE IndividualDetail (
    PartnerId VARCHAR(36) PRIMARY KEY,
    IdNumber VARCHAR(10),   -- 身分證字號
    BirthDate DATE,
    Gender VARCHAR(10),
    FOREIGN KEY (PartnerId) REFERENCES Partner(PartnerId)
);

CREATE TABLE CustomerDetail (
    PartnerId VARCHAR(36) PRIMARY KEY,
    LoyaltyPoints INT DEFAULT 0,
    PaymentMethod VARCHAR(50),
    FOREIGN KEY (PartnerId) REFERENCES Partner(PartnerId)
);

CREATE TABLE SupplierDetail (
    PartnerId VARCHAR(36) PRIMARY KEY,
    SupplierType VARCHAR(50),
    ContractTerms TEXT,
    Rating DECIMAL(2,1),
    FOREIGN KEY (PartnerId) REFERENCES Partner(PartnerId)
);

CREATE TABLE ContactPerson (
    ContactPersonId VARCHAR(36) PRIMARY KEY,
    Name VARCHAR(255) NOT NULL,
    Phone VARCHAR(50),
    Email VARCHAR(255)
);

CREATE TABLE SupplierContact (
    SupplierId VARCHAR(36),
    ContactPersonId VARCHAR(36),
    PRIMARY KEY (SupplierId, ContactPersonId),
    FOREIGN KEY (SupplierId) REFERENCES SupplierDetail(PartnerId),
    FOREIGN KEY (ContactPersonId) REFERENCES ContactPerson(ContactPersonId)
);

CREATE TABLE CustomerContact (
    CustomerId VARCHAR(36),
    ContactPersonId VARCHAR(36),
    PRIMARY KEY (CustomerId, ContactPersonId),
    FOREIGN KEY (CustomerId) REFERENCES CustomerDetail(PartnerId),
    FOREIGN KEY (ContactPersonId) REFERENCES ContactPerson(ContactPersonId)
);

-- 庫存品相關表
CREATE TABLE ItemCategory (
    CategoryId VARCHAR(36) PRIMARY KEY,
    Name VARCHAR(100) NOT NULL,
    ParentCategoryId VARCHAR(36),
    FOREIGN KEY (ParentCategoryId) REFERENCES ItemCategory(CategoryId)
);

CREATE TABLE Item (
    ItemId VARCHAR(36) PRIMARY KEY,
    Name VARCHAR(255) NOT NULL,
    CategoryId VARCHAR(36),
    Description TEXT,
    StandardCost DECIMAL(18,2),
    FOREIGN KEY (CategoryId) REFERENCES ItemCategory(CategoryId)
);

CREATE TABLE PriceType (
    PriceTypeId VARCHAR(36) PRIMARY KEY,
    Name VARCHAR(50) NOT NULL,
    Description VARCHAR(255)
);

CREATE TABLE ItemPrice (
    ItemPriceId VARCHAR(36) PRIMARY KEY,
    ItemId VARCHAR(36) NOT NULL,
    PriceTypeId VARCHAR(36) NOT NULL,
    Price DECIMAL(18,2) NOT NULL,
    EffectiveDate DATE,
    ExpirationDate DATE,
    FOREIGN KEY (ItemId) REFERENCES Item(ItemId),
    FOREIGN KEY (PriceTypeId) REFERENCES PriceType(PriceTypeId)
);

-- 倉庫與庫存相關表
CREATE TABLE Warehouse (
    WarehouseId VARCHAR(36) PRIMARY KEY,
    Name VARCHAR(100) NOT NULL,
    Location TEXT,
    CostMethod VARCHAR(20) CHECK (CostMethod IN ('FIFO', 'LIFO', 'WeightedAverage', 'Standard'))
);

CREATE TABLE Inventory (
    InventoryId VARCHAR(36) PRIMARY KEY,
    WarehouseId VARCHAR(36) NOT NULL,
    ItemId VARCHAR(36) NOT NULL,
    Quantity DECIMAL(18,2) NOT NULL DEFAULT 0,
    AverageCost DECIMAL(18,2),
    FOREIGN KEY (WarehouseId) REFERENCES Warehouse(WarehouseId),
    FOREIGN KEY (ItemId) REFERENCES Item(ItemId),
    UNIQUE (WarehouseId, ItemId)
);

CREATE TABLE ItemBatch (
    ItemBatchId VARCHAR(36) PRIMARY KEY,
    WarehouseId VARCHAR(36) NOT NULL,
    ItemId VARCHAR(36) NOT NULL,
    Quantity DECIMAL(18,2) NOT NULL,
    UnitCost DECIMAL(18,2) NOT NULL,
    ReceiptDate DATETIME NOT NULL,
    ItemReceiptId VARCHAR(36),
    FOREIGN KEY (WarehouseId) REFERENCES Warehouse(WarehouseId),
    FOREIGN KEY (ItemId) REFERENCES Item(ItemId),
    FOREIGN KEY (ItemReceiptId) REFERENCES ItemReceipt(ItemReceiptId)
);

CREATE TABLE ItemTransfer (
    ItemTransferId VARCHAR(36) PRIMARY KEY,
    SourceWarehouseId VARCHAR(36) NOT NULL,
    TargetWarehouseId VARCHAR(36) NOT NULL,
    TransferDate DATETIME NOT NULL,
    Status VARCHAR(20) NOT NULL,
    FOREIGN KEY (SourceWarehouseId) REFERENCES Warehouse(WarehouseId),
    FOREIGN KEY (TargetWarehouseId) REFERENCES Warehouse(WarehouseId)
);

CREATE TABLE ItemTransferDetail (
    ItemTransferDetailId VARCHAR(36) PRIMARY KEY,
    ItemTransferId VARCHAR(36) NOT NULL,
    ItemId VARCHAR(36) NOT NULL,
    Quantity DECIMAL(18,2) NOT NULL,
    UnitCost DECIMAL(18,2) NOT NULL,
    ItemBatchId VARCHAR(36),
    FOREIGN KEY (ItemTransferId) REFERENCES ItemTransfer(ItemTransferId),
    FOREIGN KEY (ItemId) REFERENCES Item(ItemId),
    FOREIGN KEY (ItemBatchId) REFERENCES ItemBatch(ItemBatchId)
);

CREATE TABLE InventoryAdjustment (
    InventoryAdjustmentId VARCHAR(36) PRIMARY KEY,
    WarehouseId VARCHAR(36) NOT NULL,
    AdjustmentDate DATETIME NOT NULL,
    Status VARCHAR(20) NOT NULL,
    Reason VARCHAR(255),
    FOREIGN KEY (WarehouseId) REFERENCES Warehouse(WarehouseId)
);

CREATE TABLE InventoryAdjustmentDetail (
    InventoryAdjustmentDetailId VARCHAR(36) PRIMARY KEY,
    InventoryAdjustmentId VARCHAR(36) NOT NULL,
    ItemId VARCHAR(36) NOT NULL,
    ActualQuantity DECIMAL(18,2) NOT NULL,
    SystemQuantity DECIMAL(18,2) NOT NULL,
    AdjustmentQuantity DECIMAL(18,2) NOT NULL,
    UnitCost DECIMAL(18,2) NOT NULL,
    ItemBatchId VARCHAR(36),
    FOREIGN KEY (InventoryAdjustmentId) REFERENCES InventoryAdjustment(InventoryAdjustmentId),
    FOREIGN KEY (ItemId) REFERENCES Item(ItemId),
    FOREIGN KEY (ItemBatchId) REFERENCES ItemBatch(ItemBatchId)
);

-- 採購相關表
CREATE TABLE PurchaseOrder (
    PurchaseOrderId VARCHAR(36) PRIMARY KEY,
    SupplierId VARCHAR(36) NOT NULL,
    OrderDate DATETIME NOT NULL,
    Status VARCHAR(20) NOT NULL,
    TotalAmount DECIMAL(18,2),
    FOREIGN KEY (SupplierId) REFERENCES Partner(PartnerId)
);

CREATE TABLE PurchaseOrderDetail (
    PurchaseOrderDetailId VARCHAR(36) PRIMARY KEY,
    PurchaseOrderId VARCHAR(36) NOT NULL,
    ItemId VARCHAR(36) NOT NULL,
    Quantity DECIMAL(18,2) NOT NULL,
    UnitPrice DECIMAL(18,2) NOT NULL,
    FOREIGN KEY (PurchaseOrderId) REFERENCES PurchaseOrder(PurchaseOrderId),
    FOREIGN KEY (ItemId) REFERENCES Item(ItemId)
);

CREATE TABLE ItemReceipt (
    ItemReceiptId VARCHAR(36) PRIMARY KEY,
    PurchaseOrderId VARCHAR(36) NOT NULL,
    WarehouseId VARCHAR(36) NOT NULL,
    ReceiptDate DATETIME NOT NULL,
    FOREIGN KEY (PurchaseOrderId) REFERENCES PurchaseOrder(PurchaseOrderId),
    FOREIGN KEY (WarehouseId) REFERENCES Warehouse(WarehouseId)
);

CREATE TABLE ItemReceiptDetail (
    ItemReceiptDetailId VARCHAR(36) PRIMARY KEY,
    ItemReceiptId VARCHAR(36) NOT NULL,
    ItemId VARCHAR(36) NOT NULL,
    Quantity DECIMAL(18,2) NOT NULL,
    UnitCost DECIMAL(18,2) NOT NULL,
    FOREIGN KEY (ItemReceiptId) REFERENCES ItemReceipt(ItemReceiptId),
    FOREIGN KEY (ItemId) REFERENCES Item(ItemId)
);

CREATE TABLE PurchaseReturn (
    PurchaseReturnId VARCHAR(36) PRIMARY KEY,
    PurchaseOrderId VARCHAR(36),
    ReturnDate DATETIME NOT NULL,
    Reason TEXT,
    FOREIGN KEY (PurchaseOrderId) REFERENCES PurchaseOrder(PurchaseOrderId)
);

CREATE TABLE PurchaseReturnDetail (
    PurchaseReturnDetailId VARCHAR(36) PRIMARY KEY,
    PurchaseReturnId VARCHAR(36) NOT NULL,
    ItemId VARCHAR(36) NOT NULL,
    Quantity DECIMAL(18,2) NOT NULL,
    ItemBatchId VARCHAR(36),
    FOREIGN KEY (PurchaseReturnId) REFERENCES PurchaseReturn(PurchaseReturnId),
    FOREIGN KEY (ItemId) REFERENCES Item(ItemId),
    FOREIGN KEY (ItemBatchId) REFERENCES ItemBatch(ItemBatchId)
);

-- 銷售相關表
CREATE TABLE SalesOrder (
    SalesOrderId VARCHAR(36) PRIMARY KEY,
    CustomerId VARCHAR(36) NOT NULL,
    OrderDate DATETIME NOT NULL,
    Status VARCHAR(20) NOT NULL,
    TotalAmount DECIMAL(18,2),
    FOREIGN KEY (CustomerId) REFERENCES Partner(PartnerId)
);

CREATE TABLE SalesOrderDetail (
    SalesOrderDetailId VARCHAR(36) PRIMARY KEY,
    SalesOrderId VARCHAR(36) NOT NULL,
    ItemId VARCHAR(36) NOT NULL,
    Quantity DECIMAL(18,2) NOT NULL,
    UnitPrice DECIMAL(18,2) NOT NULL,
    UnitCost DECIMAL(18,2),
    GrossProfit DECIMAL(18,2),
    FOREIGN KEY (SalesOrderId) REFERENCES SalesOrder(SalesOrderId),
    FOREIGN KEY (ItemId) REFERENCES Item(ItemId)
);

CREATE TABLE Shipment (
    ShipmentId VARCHAR(36) PRIMARY KEY,
    SalesOrderId VARCHAR(36) NOT NULL,
    WarehouseId VARCHAR(36) NOT NULL,
    ShipmentDate DATETIME NOT NULL,
    Carrier VARCHAR(100),
    TrackingNumber VARCHAR(100),
    FOREIGN KEY (SalesOrderId) REFERENCES SalesOrder(SalesOrderId),
    FOREIGN KEY (WarehouseId) REFERENCES Warehouse(WarehouseId)
);

CREATE TABLE ShipmentDetail (
    ShipmentDetailId VARCHAR(36) PRIMARY KEY,
    ShipmentId VARCHAR(36) NOT NULL,
    ItemId VARCHAR(36) NOT NULL,
    Quantity DECIMAL(18,2) NOT NULL,
    ItemBatchId VARCHAR(36),
    FOREIGN KEY (ShipmentId) REFERENCES Shipment(ShipmentId),
    FOREIGN KEY (ItemId) REFERENCES Item(ItemId),
    FOREIGN KEY (ItemBatchId) REFERENCES ItemBatch(ItemBatchId)
);

CREATE TABLE SalesReturn (
    SalesReturnId VARCHAR(36) PRIMARY KEY,
    SalesOrderId VARCHAR(36),
    ReturnDate DATETIME NOT NULL,
    Reason TEXT,
    FOREIGN KEY (SalesOrderId) REFERENCES SalesOrder(SalesOrderId)
);

CREATE TABLE SalesReturnDetail (
    SalesReturnDetailId VARCHAR(36) PRIMARY KEY,
    SalesReturnId VARCHAR(36) NOT NULL,
    ItemId VARCHAR(36) NOT NULL,
    Quantity DECIMAL(18,2) NOT NULL,
    ItemBatchId VARCHAR(36),
    FOREIGN KEY (SalesReturnId) REFERENCES SalesReturn(SalesReturnId),
    FOREIGN KEY (ItemId) REFERENCES Item(ItemId),
    FOREIGN KEY (ItemBatchId) REFERENCES ItemBatch(ItemBatchId)
);

-- 加工與包裝轉換相關表
CREATE TABLE Bom (
    BomId VARCHAR(36) PRIMARY KEY,
    FinishedItemId VARCHAR(36) NOT NULL,
    ComponentItemId VARCHAR(36) NOT NULL,
    Quantity DECIMAL(18,2) NOT NULL,
    FOREIGN KEY (FinishedItemId) REFERENCES Item(ItemId),
    FOREIGN KEY (ComponentItemId) REFERENCES Item(ItemId)
);

CREATE TABLE ProcessingOrder (
    ProcessingOrderId VARCHAR(36) PRIMARY KEY,
    BomId VARCHAR(36),
    StartDate DATETIME NOT NULL,
    EndDate DATETIME,
    Status VARCHAR(20) NOT NULL,
    FOREIGN KEY (BomId) REFERENCES Bom(BomId)
);

CREATE TABLE ProcessingOrderInput (
    ProcessingOrderInputId VARCHAR(36) PRIMARY KEY,
    ProcessingOrderId VARCHAR(36) NOT NULL,
    ItemId VARCHAR(36) NOT NULL,
    Quantity DECIMAL(18,2) NOT NULL,
    ItemBatchId VARCHAR(36),
    FOREIGN KEY (ProcessingOrderId) REFERENCES ProcessingOrder(ProcessingOrderId),
    FOREIGN KEY (ItemId) REFERENCES Item(ItemId),
    FOREIGN KEY (ItemBatchId) REFERENCES ItemBatch(ItemBatchId)
);

CREATE TABLE ProcessingOrderOutput (
    ProcessingOrderOutputId VARCHAR(36) PRIMARY KEY,
    ProcessingOrderId VARCHAR(36) NOT NULL,
    ItemId VARCHAR(36) NOT NULL,
    Quantity DECIMAL(18,2) NOT NULL,
    FOREIGN KEY (ProcessingOrderId) REFERENCES ProcessingOrder(ProcessingOrderId),
    FOREIGN KEY (ItemId) REFERENCES Item(ItemId)
);

-- 財務相關表
CREATE TABLE AccountsReceivable (
    AccountsReceivableId VARCHAR(36) PRIMARY KEY,
    CustomerId VARCHAR(36) NOT NULL,
    SalesOrderId VARCHAR(36),
    Amount DECIMAL(18,2) NOT NULL,
    DueDate DATETIME NOT NULL,
    Status VARCHAR(20) NOT NULL,
    FOREIGN KEY (CustomerId) REFERENCES Partner(PartnerId),
    FOREIGN KEY (SalesOrderId) REFERENCES SalesOrder(SalesOrderId)
);

CREATE TABLE AccountsPayable (
    AccountsPayableId VARCHAR(36) PRIMARY KEY,
    SupplierId VARCHAR(36) NOT NULL,
    PurchaseOrderId VARCHAR(36),
    Amount DECIMAL(18,2) NOT NULL,
    DueDate DATETIME NOT NULL,
    Status VARCHAR(20) NOT NULL,
    FOREIGN KEY (SupplierId) REFERENCES Partner(PartnerId),
    FOREIGN KEY (PurchaseOrderId) REFERENCES PurchaseOrder(PurchaseOrderId)
);

CREATE TABLE Payment (
    PaymentId VARCHAR(36) PRIMARY KEY,
    PartnerId VARCHAR(36) NOT NULL,
    Amount DECIMAL(18,2) NOT NULL,
    PaymentDate DATETIME NOT NULL,
    Type VARCHAR(20) CHECK (Type IN ('Receivable', 'Payable')),
    AccountsReceivableId VARCHAR(36),
    AccountsPayableId VARCHAR(36),
    FOREIGN KEY (PartnerId) REFERENCES Partner(PartnerId),
    FOREIGN KEY (AccountsReceivableId) REFERENCES AccountsReceivable(AccountsReceivableId),
    FOREIGN KEY (AccountsPayableId) REFERENCES AccountsPayable(AccountsPayableId)
);

CREATE TABLE AccountClosing (
    AccountClosingId VARCHAR(36) PRIMARY KEY,
    ClosingDate DATETIME NOT NULL,
    TotalCost DECIMAL(18,2),
    TotalRevenue DECIMAL(18,2),
    Profit DECIMAL(18,2)
);

CREATE TABLE OrderLog (
    OrderLogId VARCHAR(36) PRIMARY KEY,
    OrderType VARCHAR(20) CHECK (OrderType IN ('Purchase', 'Sales')),
    OrderId VARCHAR(36) NOT NULL,
    ChangeDate DATETIME NOT NULL,
    ChangeDescription TEXT NOT NULL,
    ChangedBy VARCHAR(255)
);

```

---

## 4. 資料表關聯

- **合作夥伴**：
    - `Partner.PartnerId` 與 `CustomerDetail`、`SupplierDetail` 一對一。
    - `ContactPerson` 透過 `SupplierContact` 和 `CustomerContact` 多對多關聯。
- **庫存品與價格**：
    - `Item.ItemId` 與 `ItemCategory.CategoryId` 多對一。
    - `ItemPrice` 關聯 `Item` 和 `PriceType`。
- **倉庫與庫存**：
    - `Warehouse.WarehouseId` 與 `Inventory`、`ItemBatch`、`ItemTransfer`、`InventoryAdjustment` 一對多。
- **採購與銷售**：
    - 採購：`PurchaseOrder` → `ItemReceipt` → `PurchaseReturn`。
    - 銷售：`SalesOrder` → `Shipment` → `SalesReturn`。
- **加工與包裝轉換**：
    - `Bom` 定義物料關係，`ProcessingOrder` 管理流程。
- **財務**：
    - `AccountsReceivable`、`AccountsPayable`、`Payment` 追蹤帳款，`AccountClosing` 總結財務。
- **錯誤處理**：
    - `OrderLog` 記錄修改，`PurchaseReturn`、`SalesReturn`、`InventoryAdjustment` 修正錯誤。

---

## 5. 結論

此進銷存系統資料庫設計支援多倉庫庫存（FIFO、LIFO、加權平均、標準成本）、採購、銷售、退貨、加工、包裝轉換、盤點、毛利計算和錯誤處理。命名一致（主鍵為 `<表名>Id`），`ItemBatch` 支援成本追蹤，`InventoryAdjustment` 處理盤點，`SalesOrderDetail` 記錄毛利。如果需要 C# Entity Framework Code First 程式碼、查詢範例或進一步調整，請隨時告知！