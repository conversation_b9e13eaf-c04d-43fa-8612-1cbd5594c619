"use client";

import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, Row, Col, Tabs, Button, message, Switch, TreeSelect, Card, Space, Tag, Tooltip, Alert, DatePicker } from 'antd';
import { UserOutlined, TeamOutlined, ContactsOutlined, ShopOutlined, SettingOutlined, ApartmentOutlined, PlusOutlined, CalendarOutlined } from '@ant-design/icons';
import { Partner, CustomerCategory, SupplierCategory } from '@/services/ims/partner';
import { buildCustomerCategoryTree } from '@/services/ims/CustomerCategoryService';
import { buildSupplierCategoryTree } from '@/services/ims/SupplierCategoryService';
import SettlementDayPicker from '@/app/ims/components/shared/SettlementDayPicker';

const { TabPane } = Tabs;

interface PartnerFormModalProps {
  visible: boolean;
  onClose: () => void;
  selectedPartner: Partner | null;
  onSubmit: (values: any) => Promise<void>;
  loading?: boolean;
  customerCategories: CustomerCategory[];
  supplierCategories: SupplierCategory[];
  onCategoryDataChange?: () => void;
}

const PartnerFormModal: React.FC<PartnerFormModalProps> = ({
  visible,
  onClose,
  selectedPartner,
  onSubmit,
  loading = false,
  customerCategories = [],
  supplierCategories = [],
  onCategoryDataChange,
}) => {
  const [form] = Form.useForm();
  const [partnerType, setPartnerType] = useState<'individual' | 'organization'>('individual');
  const [isCustomer, setIsCustomer] = useState(false);
  const [isSupplier, setIsSupplier] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // 移動端檢測
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  useEffect(() => {
    if (visible) {
      if (selectedPartner) {
        const type = selectedPartner.organizationDetail ? 'organization' : 'individual';
        setPartnerType(type);
        setIsCustomer(!!selectedPartner.customerDetail);
        setIsSupplier(!!selectedPartner.supplierDetail);

        form.setFieldsValue({
          isStop: selectedPartner.isStop || false,
          individualDetail: selectedPartner.individualDetail,
          organizationDetail: selectedPartner.organizationDetail,
          customerDetail: selectedPartner.customerDetail,
          supplierDetail: selectedPartner.supplierDetail,
        });
      } else {
        setPartnerType('individual');
        setIsCustomer(false);
        setIsSupplier(false);
        form.resetFields();
        form.setFieldsValue({
          isStop: true  // 預設為啟用狀態 (前端顯示邏輯)
        });
      }
    }
  }, [visible, selectedPartner, form]);

  // 表單驗證函數 - 確保個人和組織資料互斥
  const validatePartnerData = (values: any): { isValid: boolean; message?: string } => {
    // 檢查是否選擇了夥伴類型
    if (!partnerType) {
      return { isValid: false, message: "請選擇夥伴類型（個人或組織）" };
    }

    // 檢查是否至少選擇了客戶或供應商角色
    // if (!isCustomer && !isSupplier) {
    //   return { isValid: false, message: "請至少選擇一個角色（客戶或供應商）" };
    // }

    // 根據夥伴類型驗證必要欄位
    if (partnerType === 'individual') {
      if (!values.individualDetail?.lastName?.trim()) {
        return { isValid: false, message: "個人夥伴必須填寫姓氏" };
      }
      if (!values.individualDetail?.firstName?.trim()) {
        return { isValid: false, message: "個人夥伴必須填寫名字" };
      }
    } else if (partnerType === 'organization') {
      if (!values.organizationDetail?.companyName?.trim()) {
        return { isValid: false, message: "組織夥伴必須填寫公司名稱" };
      }
    }

    return { isValid: true };
  };

  const handleFormSubmit = async (values: any) => {
    try {
      // 執行表單驗證
      const validation = validatePartnerData(values);
      if (!validation.isValid) {
        message.error(validation.message);
        return;
      }

      const submitData: Partial<Partner> = {
        partnerID: selectedPartner?.partnerID, // Include partnerID if editing
      };

      // 設定個人或組織資料（互斥）
      if (partnerType === 'individual') {
        submitData.individualDetail = {
          ...values.individualDetail,
        };
        // 只有編輯時才設定 partnerID，新增時讓後端自動生成
        if (selectedPartner?.partnerID && submitData.individualDetail) {
          submitData.individualDetail.partnerID = selectedPartner.partnerID;
        }
        // 確保組織資料為空
        submitData.organizationDetail = undefined;
      } else if (partnerType === 'organization') {
        submitData.organizationDetail = {
          ...values.organizationDetail,
        };
        // 只有編輯時才設定 partnerID，新增時讓後端自動生成
        if (selectedPartner?.partnerID && submitData.organizationDetail) {
          submitData.organizationDetail.partnerID = selectedPartner.partnerID;
        }
        // 確保個人資料為空
        submitData.individualDetail = undefined;
      }

      // 設定客戶資料
      if (isCustomer) {
        submitData.customerDetail = {
          ...values.customerDetail,
        };
        // 只有編輯時才設定 partnerID，新增時讓後端自動生成
        if (selectedPartner?.partnerID && submitData.customerDetail) {
          submitData.customerDetail.partnerID = selectedPartner.partnerID;
        }
      } else {
        submitData.customerDetail = undefined;
      }

      // 設定供應商資料
      if (isSupplier) {
        submitData.supplierDetail = {
          ...values.supplierDetail,
        };
        // 只有編輯時才設定 partnerID，新增時讓後端自動生成
        if (selectedPartner?.partnerID && submitData.supplierDetail) {
          submitData.supplierDetail.partnerID = selectedPartner.partnerID;
        }
      } else {
        submitData.supplierDetail = undefined;
      }

      console.log('✅ PartnerFormModal: 提交資料驗證通過', {
        partnerType,
        isCustomer,
        isSupplier,
        submitData
      });

      await onSubmit(submitData);
    } catch (error) {
      console.error('❌ PartnerFormModal: Error submitting form:', error);
      message.error('提交失敗，請重試');
    }
  };

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <ContactsOutlined style={{ color: '#1890ff' }} />
          <span>{selectedPartner ? '編輯商業夥伴' : '新增商業夥伴'}</span>
        </div>
      }
      open={visible}
      onCancel={onClose}
      onOk={() => form.submit()}
      confirmLoading={loading}
      width={isMobile ? '95%' : 900}
    >
      <Form form={form} layout="vertical" onFinish={handleFormSubmit} initialValues={{}}>
        {/* 角色管理區域 */}
        <Card
          title={
            <Space>
              <SettingOutlined />
              <span>角色設定</span>
            </Space>
          }
          size="small"
          style={{ marginBottom: 16 }}
        >
          <Row gutter={16}>
            <Col span={isMobile ? 24 : 8}>
              <Form.Item label="客戶角色" valuePropName="checked">
                <Switch
                  checked={isCustomer}
                  onChange={setIsCustomer}
                  checkedChildren="啟用"
                  unCheckedChildren="停用"
                />
              </Form.Item>
            </Col>
            <Col span={isMobile ? 24 : 8}>
              <Form.Item label="供應商角色" valuePropName="checked">
                <Switch
                  checked={isSupplier}
                  onChange={setIsSupplier}
                  checkedChildren="啟用"
                  unCheckedChildren="停用"
                />
              </Form.Item>
            </Col>
            <Col span={isMobile ? 24 : 8}>
              <Form.Item
                label="夥伴狀態"
                name="isStop"
                valuePropName="checked"
                tooltip="控制夥伴是否啟用"
              >
                <Switch
                  checkedChildren="啟用"
                  unCheckedChildren="停用"
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* 夥伴類型選擇區域 */}
        <Card
          title={
            <Space>
              <TeamOutlined />
              <span>夥伴類型</span>
              <Tag color="orange">必選其一</Tag>
            </Space>
          }
          size="small"
          style={{ marginBottom: 16 }}
        >
          <Alert
            message="重要提醒"
            description="商業夥伴必須選擇為「個人」或「組織」其中一種類型，不可同時為兩種類型。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Tabs
            activeKey={partnerType}
            onChange={(key) => {
              setPartnerType(key as any);
              // 切換類型時清除另一種類型的表單資料
              if (key === 'individual') {
                form.setFieldsValue({ organizationDetail: undefined });
              } else {
                form.setFieldsValue({ individualDetail: undefined });
              }
            }}
            type="card"
            size="small"
          >
          <TabPane tab={<span><UserOutlined />個人</span>} key="individual">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="姓氏"
                  name={['individualDetail', 'lastName']}
                  rules={[
                    {
                      required: partnerType === 'individual',
                      message: '個人夥伴必須填寫姓氏'
                    }
                  ]}
                >
                  <Input placeholder="請輸入姓氏" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="名字"
                  name={['individualDetail', 'firstName']}
                  rules={[
                    {
                      required: partnerType === 'individual',
                      message: '個人夥伴必須填寫名字'
                    }
                  ]}
                >
                  <Input placeholder="請輸入名字" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="身分證號" name={['individualDetail', 'identificationNumber']}>
                  <Input placeholder="請輸入身分證號" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label={
                    <Space>
                      <CalendarOutlined style={{ color: '#1890ff' }} />
                      <span>生日</span>
                    </Space>
                  }
                  name={['individualDetail', 'birthDate']}
                >
                  <DatePicker
                    placeholder="請選擇生日"
                    style={{ width: '100%' }}
                    format="YYYY-MM-DD"
                  />
                </Form.Item>
              </Col>
            </Row>
          </TabPane>
          <TabPane tab={<span><TeamOutlined />組織</span>} key="organization">
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                  label="公司名稱"
                  name={['organizationDetail', 'companyName']}
                  rules={[
                    {
                      required: partnerType === 'organization',
                      message: '組織夥伴必須填寫公司名稱'
                    }
                  ]}
                >
                  <Input placeholder="請輸入公司名稱" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="統一編號" name={['organizationDetail', 'bussinessId']}>
                  <Input placeholder="請輸入統一編號" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="稅務編號" name={['organizationDetail', 'taxId']}>
                  <Input placeholder="請輸入稅務編號" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="負責人" name={['organizationDetail', 'responsiblePerson']}>
                  <Input placeholder="請輸入負責人" />
                </Form.Item>
              </Col>
            </Row>
          </TabPane>
        </Tabs>
        </Card>

        {/* 客戶詳細設定 */}
        {isCustomer && (
          <Card
            title={
              <Space>
                <ContactsOutlined />
                <span>客戶設定</span>
              </Space>
            }
            size="small"
            style={{ marginTop: 16 }}
          >
            <Row gutter={16}>
              <Col span={isMobile ? 24 : 12}>
                <Form.Item label="客戶編號" name={['customerDetail', 'customerCode']}>
                  <Input placeholder="請輸入客戶編號" />
                </Form.Item>
              </Col>
              <Col span={isMobile ? 24 : 12}>
                <Form.Item label="客戶分類" name={['customerDetail', 'customerCategoryID']}>
                  <TreeSelect
                    placeholder="請選擇客戶分類"
                    allowClear
                    treeData={buildCustomerCategoryTree(customerCategories).map(category => ({
                      title: category.name,
                      value: category.customerCategoryID,
                      key: category.customerCategoryID,
                      children: category.children?.map(child => ({
                        title: child.name,
                        value: child.customerCategoryID,
                        key: child.customerCategoryID,
                      })) || []
                    }))}
                  />
                </Form.Item>
              </Col>
              <Col span={isMobile ? 24 : 12}>
                <Form.Item
                  label={
                    <Space>
                      <CalendarOutlined style={{ color: '#1890ff' }} />
                      <span>結算日</span>
                    </Space>
                  }
                  name={['customerDetail', 'settlementDay']}
                  tooltip="選擇每月的結算日期，用於客戶應收帳款結算"
                >
                  <SettlementDayPicker placeholder="請選擇客戶結算日" />
                </Form.Item>
              </Col>
            </Row>
          </Card>
        )}

        {/* 供應商詳細設定 */}
        {isSupplier && (
          <Card
            title={
              <Space>
                <ShopOutlined />
                <span>供應商設定</span>
              </Space>
            }
            size="small"
            style={{ marginTop: 16 }}
          >
            <Row gutter={16}>
              <Col span={isMobile ? 24 : 12}>
                <Form.Item label="供應商編號" name={['supplierDetail', 'supplierCode']}>
                  <Input placeholder="請輸入供應商編號" />
                </Form.Item>
              </Col>
              <Col span={isMobile ? 24 : 12}>
                <Form.Item label="供應商分類" name={['supplierDetail', 'supplierCategoryID']}>
                  <TreeSelect
                    placeholder="請選擇供應商分類"
                    allowClear
                    treeData={buildSupplierCategoryTree(supplierCategories).map(category => ({
                      title: category.name,
                      value: category.supplierCategoryID,
                      key: category.supplierCategoryID,
                      children: category.children?.map(child => ({
                        title: child.name,
                        value: child.supplierCategoryID,
                        key: child.supplierCategoryID,
                      })) || []
                    }))}
                  />
                </Form.Item>
              </Col>
              <Col span={isMobile ? 24 : 12}>
                <Form.Item
                  label={
                    <Space>
                      <CalendarOutlined style={{ color: '#1890ff' }} />
                      <span>結算日</span>
                    </Space>
                  }
                  name={['supplierDetail', 'settlementDay']}
                  tooltip="選擇每月的結算日期，用於供應商應付帳款結算"
                >
                  <SettlementDayPicker placeholder="請選擇供應商結算日" />
                </Form.Item>
              </Col>
            </Row>
          </Card>
        )}
      </Form>
    </Modal>
  );
};

export default PartnerFormModal;