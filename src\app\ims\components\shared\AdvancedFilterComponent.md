# AdvancedFilterComponent 進階篩選組件

## 概述 / Overview

`AdvancedFilterComponent` 是一個可重用的進階篩選組件，專為 FastERP IMS 系統設計。它提供了統一的篩選體驗，支援多種篩選類型，並可在所有 IMS 模組中共享使用。

## 功能特色 / Features

### ✅ 支援的篩選類型
- **文字輸入篩選** (`input`): 適用於代碼、名稱等文字欄位
- **下拉選擇篩選** (`select`): 支援多選，適用於狀態、類型等選項
- **樹狀選擇篩選** (`treeSelect`): 支援階層結構和多選，適用於分類等

### ✅ 核心功能
- 動態新增/移除篩選條件
- 視覺化篩選狀態指示
- 統一的清除篩選功能
- 響應式設計支援
- 完整的 TypeScript 類型安全
- 可配置的篩選選項

## 使用方式 / Usage

### 基本用法

```tsx
import AdvancedFilterComponent from '@/app/ims/components/shared/AdvancedFilterComponent';
import { FilterOption, FilterStateChangeEvent } from '@/app/ims/types/filter';

// 定義篩選選項
const filterOptions: FilterOption[] = [
  {
    label: "庫存品名稱",
    value: "name",
    type: "input",
    placeholder: "輸入庫存品名稱"
  },
  {
    label: "庫存品狀態",
    value: "status",
    children: [
      { label: "啟用", value: "active" },
      { label: "停用", value: "inactive" }
    ]
  },
  {
    label: "庫存品分類",
    value: "category",
    type: "treeSelect",
    treeData: categoryTreeData,
    width: 250
  }
];

// 處理篩選變更
const handleFilterChange = (event: FilterStateChangeEvent) => {
  // 更新父組件的篩選狀態
  setFilterData({
    activeFilters: event.activeFilters,
    filterValues: event.filterValues
  });
};

// 處理清除篩選
const handleClearFilters = () => {
  // 重置父組件的篩選狀態
  setFilterData({
    activeFilters: [],
    filterValues: {}
  });
};

// 使用組件
<AdvancedFilterComponent
  filterOptions={filterOptions}
  onFilterChange={handleFilterChange}
  onClear={handleClearFilters}
  showClearButton={true}
/>
```

### 進階用法

```tsx
// 帶初始值的使用
<AdvancedFilterComponent
  filterOptions={filterOptions}
  onFilterChange={handleFilterChange}
  onClear={handleClearFilters}
  initialActiveFilters={['status', 'category']}
  initialFilterValues={{
    status: ['active'],
    category: ['cat1', 'cat2']
  }}
  showClearButton={true}
  className="custom-filter"
  disabled={loading}
/>
```

## API 介面 / API Reference

### FilterOption 介面

```typescript
interface FilterOption {
  label: string;                    // 篩選條件顯示名稱
  value: string;                    // 篩選條件鍵值
  type?: 'input' | 'select' | 'treeSelect';  // 篩選類型
  children?: Array<{ label: string; value: string }>;  // 選項列表（select 類型）
  treeData?: any[];                 // 樹狀資料（treeSelect 類型）
  placeholder?: string;             // 佔位符文字
  width?: number;                   // 控制項寬度
}
```

### FilterChangeEvent 介面

```typescript
interface FilterChangeEvent {
  filterKey: string;                // 變更的篩選鍵值
  value: any;                       // 篩選值
  activeFilters: string[];          // 所有活躍的篩選條件
  filterValues: Record<string, any>; // 所有篩選值
}
```

### AdvancedFilterProps 介面

```typescript
interface AdvancedFilterProps {
  filterOptions: FilterOption[];    // 篩選選項配置
  onFilterChange?: (event: FilterStateChangeEvent) => void;  // 篩選變更回調
  onClear?: () => void;             // 清除篩選回調
  initialActiveFilters?: string[];  // 初始活躍篩選條件
  initialFilterValues?: Record<string, any>;  // 初始篩選值
  showClearButton?: boolean;        // 是否顯示清除按鈕
  className?: string;               // 自定義樣式類
  disabled?: boolean;               // 是否禁用
}
```

## 實際應用案例 / Real-world Examples

### 1. Item 模組中的應用

```tsx
// src/app/ims/basic/item/page.tsx
const itemFilterOptions: FilterOption[] = useMemo(() => [
  {
    label: "庫存品分類",
    value: "category",
    type: "treeSelect",
    treeData: categoryTreeData,
    width: 250
  },
  {
    label: "庫存品狀態",
    value: "status",
    children: [
      { label: "啟用", value: "active" },
      { label: "停用", value: "inactive" },
    ],
  },
  {
    label: "庫存品編號",
    value: "customNO",
    type: "input",
    placeholder: "輸入庫存品編號"
  }
], [categoryTreeData]);
```

### 2. Partner 模組中的應用

```tsx
// src/app/ims/basic/partner/page.tsx
const partnerFilterOptions: FilterOption[] = useMemo(() => [
  {
    label: "夥伴類型",
    value: "type",
    children: [
      { label: "個人", value: "individual" },
      { label: "組織", value: "organization" },
    ],
  },
  {
    label: "夥伴狀態",
    value: "status",
    children: [
      { label: "活躍", value: "active" },
      { label: "非活躍", value: "inactive" },
      { label: "暫停", value: "suspended" },
    ],
  },
  {
    label: "夥伴代碼",
    value: "code",
    type: "input",
    placeholder: "輸入夥伴代碼"
  }
], []);
```

## 設計原則 / Design Principles

### 1. 一致性 (Consistency)
- 所有 IMS 模組使用相同的篩選體驗
- 統一的視覺設計和互動模式
- 標準化的 API 介面

### 2. 可重用性 (Reusability)
- 高度可配置的篩選選項
- 支援多種篩選類型
- 無業務邏輯耦合

### 3. 可擴展性 (Extensibility)
- 易於新增新的篩選類型
- 支援自定義樣式和行為
- 向後相容的 API 設計

### 4. 使用者體驗 (User Experience)
- 直觀的篩選條件管理
- 視覺化的篩選狀態指示
- 響應式設計支援

## 最佳實踐 / Best Practices

### 1. 篩選選項配置
```tsx
// ✅ 好的做法：使用 useMemo 優化效能
const filterOptions = useMemo(() => [
  // 篩選選項配置
], [dependencies]);

// ❌ 避免：每次渲染都重新創建
const filterOptions = [
  // 篩選選項配置
];
```

### 2. 事件處理
```tsx
// ✅ 好的做法：使用 useCallback 優化效能
const handleFilterChange = useCallback((event: FilterChangeEvent) => {
  // 處理篩選變更
}, [dependencies]);

// ❌ 避免：每次渲染都重新創建函數
const handleFilterChange = (event: FilterChangeEvent) => {
  // 處理篩選變更
};
```

### 3. 狀態管理
```tsx
// ✅ 好的做法：統一管理篩選狀態
const [filterData, setFilterData] = useState({
  activeFilters: [],
  filterValues: {}
});

// ❌ 避免：分散管理篩選狀態
const [activeFilters, setActiveFilters] = useState([]);
const [filterValues, setFilterValues] = useState({});
```

## 故障排除 / Troubleshooting

### 常見問題

1. **篩選值不更新**
   - 確保 `onFilterChange` 回調正確更新父組件狀態
   - 檢查 `initialFilterValues` 是否正確傳遞

2. **TreeSelect 不顯示資料**
   - 確保 `treeData` 格式正確
   - 檢查樹狀資料的 `title` 和 `value` 屬性

3. **篩選條件不生效**
   - 確保父組件正確處理 `FilterChangeEvent`
   - 檢查篩選邏輯是否正確實現

## 版本歷史 / Version History

### v1.0.0 (2025-06-24)
- 初始版本發布
- 支援 input、select、treeSelect 三種篩選類型
- 完整的 TypeScript 類型支援
- 響應式設計支援
- Item 和 Partner 模組整合完成

## 貢獻指南 / Contributing

如需新增功能或修復問題，請遵循以下步驟：

1. 確保所有變更都有完整的 TypeScript 類型定義
2. 新增相應的 JSDoc 文檔
3. 更新此文檔中的相關章節
4. 在 Item 和 Partner 模組中測試變更
5. 確保向後相容性

## 授權 / License

此組件是 FastERP 系統的一部分，遵循項目的整體授權協議。
