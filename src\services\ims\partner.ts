// =========================================================================================
// DTOs for Partner Management - Aligning with Backend Models
// =========================================================================================

/**
 * Base DTO for entities, providing common properties like creation and modification dates.
 */
export interface ModelBaseEntityDTO {
    createTime: number | null;
    createUserId: string | null;
    updateTime: number | null;
    updateUserId: string | null;
    deleteTime: number | null;
    deleteUserId: string | null;
    isDeleted: boolean;
}

// -----------------------------------------------------------------------------------------
// Region: Partner Core and Detail DTOs
// -----------------------------------------------------------------------------------------

/**
 * 商業夥伴 - 匹配後端 PartnerDTO 結構
 */
export interface Partner extends ModelBaseEntityDTO {
  partnerID: string;  // 統一使用 PartnerID (匹配後端 Guid PartnerID)
  isStop: boolean;    // 停用狀態
  individualDetail?: IndividualDetail;
  enterpriseDetail?: EnterpriseDetail;
  customerDetail?: CustomerDetail;
  supplierDetail?: SupplierDetail;
  partnerContacts: PartnerContact[];
  addresses: PartnerAddress[];
}

/**
 * 自然人詳細資訊 - 匹配後端 IndividualDetailDTO
 */
export interface IndividualDetail {
  partnerID: string;  // 統一使用 PartnerID
  lastName?: string;
  firstName?: string;
  identificationNumber?: string;
  birthDate?: Date;
}

/**
 * 企業詳細資訊 - 匹配後端 EnterpriseDetailDTO
 */
export interface EnterpriseDetail {
  partnerID: string;  // 統一使用 PartnerID
  companyName?: string;
  bussinessId?: string;
  taxId?: string;
  responsiblePerson?: string;
}

/**
 * 客戶詳細資訊 - 匹配後端 CustomerDetailDTO
 */
export interface CustomerDetail {
  partnerID: string;  // 統一使用 PartnerID
  customerCode?: string;
  customerCategoryID?: string;  // 統一使用 CustomerCategoryID
  customerCategory?: CustomerCategory;
  settlementDay?: number;
}

/**
 * 供應商詳細資訊 - 匹配後端 SupplierDetailDTO
 */
export interface SupplierDetail {
  partnerID: string;  // 統一使用 PartnerID
  supplierCode?: string;
  supplierCategoryID?: string;  // 統一使用 SupplierCategoryID
  supplierCategory?: SupplierCategory;
  settlementDay?: number;
}

/**
 * 商業夥伴聯絡人關聯 - 匹配後端 PartnerContactDTO
 */
export interface PartnerContact {
  partnerID: string;  // 統一使用 PartnerID
  contactID: string;  // 統一使用 ContactID
  contactRoleID: string;  // 統一使用 ContactRoleID
  isPrimary: boolean;
}

/**
 * 商業夥伴地址資訊 - 匹配後端 PartnerAddressDTO
 */
export interface PartnerAddress {
  partnerAddressID: string;  // 統一使用 PartnerAddressID
  partnerID: string;  // 統一使用 PartnerID
  address: string;
  city?: string;  // 縣市
  district?: string;  // 鄉鎮市區
  postalCode?: string;  // 郵遞區號
  country?: string;  // 國家地區
  isPrimary: boolean;
}

// -----------------------------------------------------------------------------------------
// Region: Category DTOs - 匹配後端分類結構
// -----------------------------------------------------------------------------------------

/**
 * 客戶分類 - 匹配後端 CustomerCategoryDTO
 */
export interface CustomerCategory extends ModelBaseEntityDTO {
  customerCategoryID: string;  // 統一使用 CustomerCategoryID
  name: string;
  description: string;
  parentID?: string | null;  // 父分類ID
  sortCode: number;  // 排序
  parent?: CustomerCategory | null;  // 父分類
  children: CustomerCategory[];  // 子分類
}

/**
 * 供應商分類 - 匹配後端 SupplierCategoryDTO
 */
export interface SupplierCategory extends ModelBaseEntityDTO {
  supplierCategoryID: string;  // 統一使用 SupplierCategoryID
  name: string;
  description: string;
  parentID?: string | null;  // 父分類ID
  sortCode: number;  // 排序
  parent?: SupplierCategory | null;  // 父分類
  children: SupplierCategory[];  // 子分類
}